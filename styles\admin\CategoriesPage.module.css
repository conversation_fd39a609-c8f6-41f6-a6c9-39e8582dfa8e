
/* styles/admin/CategoriesPage.module.css */

.container {
  padding: 20px;
  background-color: #f9f9f9;

/* CategoriesPage.module.css */
.pageContainer {
  padding: 1.5rem;
  background-color: #f9fafb;

  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  margin-bottom: 20px;
}

.title {
  font-size: 2rem;
  color: #333;
}

.addButton {
  background-color: #007bff;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
}

.addButton:hover {
  background-color: #0056b3;
}

.categoryList {
  list-style: none;
  padding: 0;
}

.categoryItem {
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categoryDetails {
  flex-grow: 1;
}

.categoryName {
  font-size: 1.2rem;
  font-weight: bold;
  color: #555;
}

.categoryDescription {
  font-size: 0.9rem;
  color: #777;
  margin-top: 5px;
}

.categoryParent {
  font-size: 0.8rem;
  color: #888;
  margin-top: 5px;
}

.categoryActions button {
  margin-left: 10px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.editButton {
  background-color: #ffc107;
  color: #333;
}

.editButton:hover {
  background-color: #e0a800;
}

.deleteButton {
  background-color: #dc3545;
=======
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
}

.addButton {
  background-color: #4f46e5;
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 0.375rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #4338ca;
}

.loading,
.error,
.noCategories {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-size: 1rem;
}

.error {
  color: #ef4444;
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  border-radius: 0.375rem;
  padding: 1rem;
}

.tableContainer {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow-x: auto;
}

.categoriesTable {
  width: 100%;
  border-collapse: collapse;
}

.categoriesTable th,
.categoriesTable td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.categoriesTable th {
  background-color: #f3f4f6;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.categoriesTable td {
  color: #111827;
}

.actionsCell button {
  margin-right: 0.5rem;
  padding: 0.3rem 0.6rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.editButton {
  background-color: #3b82f6;
  color: white;
}

.editButton:hover {
  background-color: #2563eb;
}

.deleteButton {
  background-color: #ef4444; /* Red */

  color: white;
}

.deleteButton:hover {

  background-color: #c82333;
}

.formModal {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  margin-bottom: 30px;
}

.formModal h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #333;
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;

  background-color: #dc2626; /* Darker red */
}

.deleteButton:disabled {
  background-color: #fca5a5; /* Lighter red when disabled */
  cursor: not-allowed;
  opacity: 0.7;
}

/* Modal Styles (Simplified - assuming a Modal component handles most of this) */
.modalContent {
  padding: 1.5rem;
}

.modalContent h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #111827;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;

}

.formGroup input,
.formGroup textarea,
.formGroup select {

  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.formActions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.formActions button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.saveButton {
  background-color: #28a745;

  padding: 0.6rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.25);
}

.formGroup textarea {
  min-height: 80px;
  resize: vertical;
}

.formActions {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
}

.leftActions {
  display: flex;
  gap: 0.75rem;
}

.rightActions {
  display: flex;
  gap: 0.75rem;
}

.formActions button {
  padding: 0.6rem 1.2rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid transparent;
}

.saveButton {
  background-color: #10b981;

  color: white;
}

.saveButton:hover {

  background-color: #218838;
}

.cancelButton {
  background-color: #6c757d;
  color: white;
}

.cancelButton:hover {
  background-color: #5a6268;
}

.loading,
.error {
  text-align: center;
  padding: 20px;
  font-size: 1.1rem;
}

.error { /* General error styling, can be used by pageError or specific error sections */
  color: #721c24; /* Dark red */
  background-color: #f8d7da; /* Light red */
  border: 1px solid #f5c6cb; /* Reddish border */
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
}

.pageError { /* For page-level errors, inherits from .error but can be more specific if needed */
  /* Currently no specific overrides, uses .error styling */
}


/* Feedback messages for CRUD operations */
.feedbackMessage {
  padding: 12px;
  margin: 15px 0;
  border-radius: 5px;
  text-align: center;
  font-size: 1rem;
  font-weight: bold;
}

.successFeedback {
  background-color: #d4edda; /* Light green */
  color: #155724; /* Dark green */
  border: 1px solid #c3e6cb;
}

.errorFeedback {
  background-color: #f8d7da; /* Light red */
  color: #721c24; /* Dark red */
  border: 1px solid #f5c6cb;
}

.infoFeedback {
  background-color: #d1ecf1; /* Light blue */
  color: #0c5460; /* Dark blue */
  border: 1px solid #bee5eb;

  background-color: #059669;
}

.cancelButton {
  background-color: #e5e7eb;
  color: #374151;
}

.cancelButton:hover {
  background-color: #d1d5db;
}

.errorMessage {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;

}
