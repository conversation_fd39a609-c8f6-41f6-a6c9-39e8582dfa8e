import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '@/components/Layout';
import CustomerForm from '@/components/CustomerForm';
import SquarePaymentForm from '@/components/SquarePaymentForm';
import AnimatedSection from '@/components/AnimatedSection';
import { useCustomer } from '@/contexts/CustomerContext';
import { useCart } from '@/contexts/CartContext';
import styles from '@/styles/Checkout.module.css';

export default function Checkout() {
  const router = useRouter();

  const [cart, setCart] = useState([]);
  const { customer, saveGuestCustomer, loading: customerLoading } = useCustomer();
  const [customerStep, setCustomerStep] = useState(true); // true = customer info, false = payment

  // Customer form data for editing
  const [customerFormData, setCustomerFormData] = useState(null);

  const { customer } = useCustomer();
  const { cart, clearCart, getCartItemCount } = useCart();
  
  // Form steps
  const [customerStep, setCustomerStep] = useState(true);
  const [paymentStep, setPaymentStep] = useState(false);
  
  // Customer form data for editing
  const [customerFormData, setCustomerFormData] = useState(null);
  
  // Payment processing state
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  
  // Order summary

  const [orderSummary, setOrderSummary] = useState({
    subtotal: 0,
    shipping: 0,
    total: 0,
  });

  // Initialize cart from localStorage on component mount
  useEffect(() => {
    if (getCartItemCount() === 0) {
      // Redirect to shop if cart is empty
      router.push('/shop');
      return;
    }

    // Calculate order summary
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 100 ? 0 : 10.00; // Free shipping for orders over $100

    setOrderSummary({
      subtotal,
      shipping,
      total: subtotal + shipping,
    });
  }, [cart, router, getCartItemCount]);

  // Handle customer form completion
  const handleCustomerFormComplete = (customerData) => {
    console.log('Customer form completed with data:', customerData);

    
    // If cart is empty at this point, perhaps redirect or show message
    if (getCartItemCount() === 0) {
      alert('Your cart is empty. Please add items to your cart before proceeding.');
      router.push('/shop');
      return;
    }
    

    // Store the customer form data for potential editing
    setCustomerFormData(customerData);
    setCustomerStep(false); // Proceed to payment step
    setPaymentStep(true); // Show payment form
  };


  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!customer && !customerFormData) {
      // If no customer data, show error or return to customer step
      setCustomerStep(true);
      return;
    }
=======
  // Handle editing customer information
  const handleEditCustomerInfo = () => {
    setPaymentStep(false);
    setCustomerStep(true);
    // Customer form will be pre-populated with existing data via CustomerForm component
  };


  // Handle Square payment success
  const handlePaymentSuccess = async (paymentResult) => {
    try {

      // Process the order
      const currentCustomer = customerFormData || customer;
      const orderData = {
        customer_id: currentCustomer.id,

      setIsProcessingPayment(true);
      setPaymentError('');

      if (!customer && !customerFormData) {
        alert('Customer information is missing. Please complete the customer form.');
        setCustomerStep(true);
        return;
      }

      // Create order with payment details
      const orderData = {
        customer_id: (customerFormData || customer).id,
        customer_details: {
          name: (customerFormData || customer).name,
          email: (customerFormData || customer).email,
          phone: (customerFormData || customer).phone,
          address: (customerFormData || customer).address,
          city: (customerFormData || customer).city,
          state: (customerFormData || customer).state,
          postal_code: (customerFormData || customer).postal_code,
          country: (customerFormData || customer).country,
        },

        items: cart,
        subtotal: orderSummary.subtotal,
        shipping: orderSummary.shipping,
        total: orderSummary.total,
        status: 'COMPLETED',
        payment_id: paymentResult.paymentId,
        payment_status: paymentResult.status,
        payment_details: paymentResult
      };

      const response = await fetch('/api/public/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Error creating order');
      }

      clearCart(); // Clear cart from context

      router.push({
        pathname: '/confirmation',
        query: {
          orderId: result.order.id,
          paymentId: paymentResult.paymentId,
          status: 'COMPLETED',
          total: orderSummary.total,
        },
      });
    } catch (error) {
      console.error('Order creation error:', error);
      setPaymentError('Payment successful but order creation failed. Please contact support.');
      setIsProcessingPayment(false);
    }
  };

  // Handle Square payment error
  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    const errorMsg = error.message || 'Payment failed';
    setPaymentError(`Payment failed: ${errorMsg}. Please check your card details or try another card.`);
    setIsProcessingPayment(false);
  };

  return (
    <Layout>
      <Head>
        <title>Checkout | OceanSoulSparkles</title>
        <meta name="description" content="Complete your purchase from OceanSoulSparkles. Secure checkout with multiple payment options." />
      </Head>

      <main className={styles.main}>
        <section className={styles.checkoutSection}>
          <AnimatedSection animation="fade-up">
            <h1 className={styles.pageTitle}>Checkout</h1>

            <div className={styles.checkoutContainer}>
              <div className={styles.checkoutForm}>
                {customerStep ? (
                  <div className={styles.customerFormContainer}>
                    <h2 className={styles.sectionTitle}>Customer Information</h2>
                    <CustomerForm
                      isCheckout={true}

                      editingData={customerFormData || customer}

                      editingData={customerFormData}

                      onComplete={handleCustomerFormComplete}
                      initialData={customer || {}}
                    />
                  </div>

                ) : (
                  <form onSubmit={handleSubmit}>
                    <div className={styles.formSection}>
                      <h2 className={styles.sectionTitle}>Shipping Information</h2>
                      
                      {(customerFormData || customer) && (
                        <div className={styles.customerSummary}>
                          <h3>Customer Details</h3>
                          <p><strong>Name:</strong> {(customerFormData || customer).name}</p>
                          <p><strong>Email:</strong> {(customerFormData || customer).email}</p>
                          <p><strong>Phone:</strong> {(customerFormData || customer).phone}</p>
                          {(customerFormData || customer).address && (
                            <div className={styles.addressDetails}>
                              <p><strong>Address:</strong> {(customerFormData || customer).address}</p>
                              <p><strong>City:</strong> {(customerFormData || customer).city}, {(customerFormData || customer).state} {(customerFormData || customer).postal_code}</p>
                              <p><strong>Country:</strong> {(customerFormData || customer).country}</p>
                            </div>
                          )}

                ) : paymentStep ? (
                  <div className={styles.paymentFormContainer}>
                    <h2 className={styles.sectionTitle}>Review & Payment</h2>

                    {/* Customer Review Section */}
                    {(customerFormData || customer) && (
                      <div className={styles.customerReviewSection}>
                        <div className={styles.reviewHeader}>
                          <h3>Customer Information</h3>

                          <button
                            type="button"
                            className={styles.editButton}
                            onClick={handleEditCustomerInfo}
                          >
                            Edit Information
                          </button>
                        </div>

                        <div className={styles.customerDetails}>
                          <div className={styles.detailsGrid}>
                            <div className={styles.contactInfo}>
                              <h4>Contact Details</h4>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Name:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.name}</span>
                              </div>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Email:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.email}</span>
                              </div>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Phone:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.phone}</span>
                              </div>
                            </div>

                            {(customerFormData || customer)?.address && (
                              <div className={styles.shippingInfo}>
                                <h4>Shipping Address</h4>
                                <div className={styles.addressBlock}>
                                  <div>{(customerFormData || customer)?.address}</div>
                                  <div>
                                    {(customerFormData || customer)?.city}, {(customerFormData || customer)?.state} {(customerFormData || customer)?.postal_code}
                                  </div>
                                  <div>{(customerFormData || customer)?.country}</div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {paymentError && (
                      <div className={styles.paymentError}>
                        <p>{paymentError}</p>
                      </div>
                    )}

                    <div className={styles.squarePaymentSection}>
                      <SquarePaymentForm
                        amount={orderSummary.total}
                        currency="AUD"
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        orderDetails={{
                          productName: cart.length > 1 ? 'Multiple Products' : cart[0]?.name,
                          customerName: (customerFormData || customer)?.name,
                          orderTotal: orderSummary.total,
                          subtotal: orderSummary.subtotal,
                          shippingCost: orderSummary.shipping,
                        }}
                      />
                    </div>

                    {isProcessingPayment && (
                      <div className={styles.processingOverlay}>
                        <div className={styles.processingContent}>
                          <div className={styles.loadingSpinner}></div>
                          <p>Processing your order...</p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={styles.orderCompleteContainer}>
                    <h2 className={styles.sectionTitle}>Order Complete</h2>
                    <p>Your payment has been processed successfully!</p>
                  </div>
                )}
              </div>

              <div className={styles.orderSummary}>
                <h2 className={styles.summaryTitle}>Order Summary</h2>

                <div className={styles.cartItems}>
                  {cart.map(item => (
                    <div key={item.id} className={styles.cartItem}>
                      <div className={styles.cartItemImage}>
                        <img src={item.image} alt={item.name} />
                      </div>
                      <div className={styles.cartItemDetails}>
                        <h3 className={styles.cartItemName}>{item.name}</h3>
                        <div className={styles.cartItemPrice}>
                          <span>${item.price.toFixed(2)}</span>
                          <span>x {item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className={styles.summaryDetails}>
                  <div className={styles.summaryRow}>
                    <span>Subtotal</span>
                    <span>${orderSummary.subtotal.toFixed(2)}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span>Shipping</span>
                    <span>${orderSummary.shipping.toFixed(2)}</span>
                  </div>
                  <div className={`${styles.summaryRow} ${styles.summaryTotal}`}>
                    <span>Total</span>
                    <span>${orderSummary.total.toFixed(2)}</span>
                  </div>
                </div>

                <div className={styles.policyLinks}>
                  <Link href="/policies#shipping-info">Shipping Information</Link>
                  <Link href="/policies#return-policy">Return & Refund Policy</Link>
                </div>

                <div className={styles.secureCheckout}>
                  <div className={styles.secureCheckoutHeader}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
                    </svg>
                    <span>Secure Checkout</span>
                  </div>

                  <div className={styles.paymentLogos}>
                    <img src="/images/logos/square.png" alt="Square Payments" width={80} height={20} />
                  </div>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </section>
      </main>
    </Layout>
  );
}
