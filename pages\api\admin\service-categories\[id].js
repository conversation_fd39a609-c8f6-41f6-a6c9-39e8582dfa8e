
import { supabaseAdmin } from '../../../../lib/supabase'; // Adjusted import path

// Helper function to serialize category data consistently
// Redefined here for simplicity as per subtask instructions.
// Ideally, this would be in a shared utility file.
=======
import { supabaseAdmin } from '../../../../lib/supabase'; // Adjusted path

// Helper function to serialize category data (duplicated for now)
// TODO: Consider moving to a shared utility file

const serializeCategory = (category) => {
  if (!category) return null;
  return {
    id: String(category.id || ''),
    name: String(category.name || ''),

    description: category.description ? String(category.description) : null,
    parent_id: category.parent_id ? String(category.parent_id) : null,
    created_at: category.created_at ? String(new Date(category.created_at).toISOString()) : null,
    updated_at: category.updated_at ? String(new Date(category.updated_at).toISOString()) : null,
  };
};

// Basic UUID validation (optional, but good practice)
const isValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidRegex.test(uuid);
};

export default async function handler(req, res) {
  const { id } = req.query;

  // Optional: Validate UUID format
  if (!isValidUUID(id)) {
    return res.status(400).json({ success: false, error: 'Invalid category ID format.' });
  }

  if (req.method === 'GET') {
    try {
      console.log(`🔍 Service Categories API [/${id}] - GET: Fetching category`);

      const { data: category, error } = await supabaseAdmin
        .from('service_categories')
        .select('id, name, description, parent_id, created_at, updated_at')
        .eq('id', id)
        .single();

      if (error) {
        // Log the error but check if it's a "not found" scenario for .single()
        // .single() returns an error if multiple rows are found, or other DB errors.
        // If it finds 0 rows, it returns data as null and no error object.
        console.error(`❌ Service Categories API [/${id}] - GET: Database error:`, error);
        // Specific check for PostgREST error when resource not found with `single()` if it were to throw one.
        // However, standard behavior is data=null, error=null for 0 rows.
        // This error check is more for unexpected DB errors or if multiple rows were returned by `single`.
        return res.status(500).json({ success: false, error: 'Database error occurred.', details: error.message });
      }

      if (!category) {
        // This is the primary way to check "not found" with .single()
        console.log(`⚠️ Service Categories API [/${id}] - GET: No category found.`);
        return res.status(404).json({ success: false, error: 'Category not found.' });
      }

      console.log(`✅ Service Categories API [/${id}] - GET: Found category "${category.name}"`);
      res.status(200).json({ success: true, category: serializeCategory(category) });

    } catch (error) { // Catch any other unexpected errors
      console.error(`💥 Service Categories API [/${id}] - GET: Unexpected error:`, error);
      res.status(500).json({ success: false, error: 'Failed to fetch service category.', details: error.message });
    }
  } else {
    // Handle other HTTP methods
    console.log(`🚫 Service Categories API [/${id}] - Method ${req.method} not allowed.`);
    res.setHeader('Allow', ['GET']);
    res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
=======
    description: category.description === null ? null : String(category.description || ''),
    parent_id: category.parent_id === null ? null : String(category.parent_id || ''),
  };
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, error: `Method ${req.method} not allowed.` });
  }

  const { id } = req.query;
  console.log(`🔍 Fetching category by ID: ${id}`);

  if (!id) {
    return res.status(400).json({ success: false, error: 'Category ID is required.' });
  }

  if (typeof id !== 'string') {
    return res.status(400).json({ success: false, error: 'Category ID must be a valid UUID string.' });
  }

  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(id)) {
    return res.status(400).json({ success: false, error: 'Category ID must be a valid UUID format.' });
  }

  try {
    const { data: category, error } = await supabaseAdmin
      .from('service_categories')
      .select('id, name, description, parent_id')
      .eq('id', id)
      .single();

    if (error) {
      // Check if the error is because the item was not found (PGRST116)
      // Supabase .single() throws an error if no row is found or multiple rows are found.
      if (error.code === 'PGRST116') { // PGRST116: "The result contains 0 rows"
        console.log(`ℹ️ Category with ID ${id} not found.`);
        return res.status(404).json({ success: false, error: `Category with ID ${id} not found.` });
      }
      console.error(`❌ Database error fetching category ID ${id}:`, error);
      throw error; // Throw other errors to be caught by the generic catch block
    }

    // Note: .single() will return null if no row is found (without error if `maybeSingle()` was used, but `single()` errors)
    // The PGRST116 check above handles the "not found" case for .single()
    // If for some reason .single() returned null without an error (e.g. if it was maybeSingle()), this would be a fallback.
    if (!category) {
        console.log(`ℹ️ Category with ID ${id} not found (should have been caught by PGRST116).`);
        return res.status(404).json({ success: false, error: `Category with ID ${id} not found.` });
    }

    const serializedCategory = serializeCategory(category);
    console.log(`✅ Category ID ${id} fetched and serialized:`, serializedCategory);
    res.status(200).json({ success: true, category: serializedCategory });

  } catch (error) {
    console.error(`💥 Error fetching category ID ${parsedId}:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to fetch category with ID ${parsedId}.`,
      details: error.message,
    });

  }
}
